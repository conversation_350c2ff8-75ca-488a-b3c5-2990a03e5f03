/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ComplianceAgentService.java
 * 包    名：com.whiskerguard.ai.service.agent
 * 描    述：合规智能体核心服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.enumeration.AgentTaskStatus;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskPriority;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.security.SecurityUtils;
import com.whiskerguard.ai.service.agent.business.ContractReviewAgentService;
import com.whiskerguard.ai.service.agent.business.PolicyReviewAgentService;
import com.whiskerguard.ai.service.agent.business.RegulationInternalizationAgentService;
import com.whiskerguard.ai.service.agent.core.TaskOrchestratorService;
import com.whiskerguard.ai.service.agent.dto.AgentTaskRequestDTO;
import com.whiskerguard.ai.service.agent.dto.AgentTaskResponseDTO;
import com.whiskerguard.ai.service.dto.ContractReviewRequestDTO;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import com.whiskerguard.ai.service.dto.PolicyReviewRequestDTO;
import com.whiskerguard.ai.service.dto.PolicyReviewResponseDTO;
import com.whiskerguard.ai.service.dto.RegulationInternalizationRequestDTO;
import com.whiskerguard.ai.service.dto.RegulationInternalizationResponseDTO;
import com.whiskerguard.ai.service.mapper.AgentTaskMapper;
import jakarta.validation.Valid;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合规智能体核心服务
 * <p>
 * 提供统一的Agent服务入口，协调各个业务Agent的执行。
 * 负责任务创建、状态管理、结果整合等核心功能。
 *
 * 主要功能：
 * 1. 任务创建和管理
 * 2. 业务Agent调度
 * 3. 执行状态跟踪
 * 4. 结果整合返回
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class ComplianceAgentService {

    private static final Logger log = LoggerFactory.getLogger(ComplianceAgentService.class);

    private final AgentTaskRepository agentTaskRepository;
    private final AgentTaskMapper agentTaskMapper;
    private final TaskOrchestratorService taskOrchestratorService;
    private final RegulationInternalizationAgentService regulationInternalizationService;
    private final PolicyReviewAgentService policyReviewService;
    private final ContractReviewAgentService contractReviewService;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public ComplianceAgentService(
        AgentTaskRepository agentTaskRepository,
        AgentTaskMapper agentTaskMapper,
        TaskOrchestratorService taskOrchestratorService,
        RegulationInternalizationAgentService regulationInternalizationService,
        PolicyReviewAgentService policyReviewService,
        ContractReviewAgentService contractReviewService
    ) {
        this.agentTaskRepository = agentTaskRepository;
        this.agentTaskMapper = agentTaskMapper;
        this.taskOrchestratorService = taskOrchestratorService;
        this.regulationInternalizationService = regulationInternalizationService;
        this.policyReviewService = policyReviewService;
        this.contractReviewService = contractReviewService;
    }

    /**
     * 创建Agent任务
     *
     * @param request 任务请求
     * @return 任务响应
     */
    public AgentTaskResponseDTO createTask(@Valid AgentTaskRequestDTO request) {
        log.info("创建Agent任务，类型: {}, 租户: {}", request.getTaskType(), request.getTenantId());

        try {
            // 1. 创建任务实体
            AgentTask agentTask = createAgentTaskEntity(request);
            final AgentTask savedTask = agentTaskRepository.save(agentTask);

            // 2. 异步执行任务
            executeTaskAsync(savedTask);

            // 3. 返回任务响应
            return buildTaskResponse(savedTask, null);
        } catch (Exception e) {
            log.error("创建Agent任务失败", e);
            throw new RuntimeException("创建Agent任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    @Transactional(readOnly = true)
    public AgentTaskResponseDTO getTaskStatus(Long taskId) {
        log.debug("获取任务状态，任务ID: {}", taskId);

        Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        AgentTask task = taskOpt.orElseThrow(); // Fixed: replaced get() with orElseThrow()
        return buildTaskResponse(task, task.getResponseData());
    }

    /**
     * 获取任务结果
     *
     * @param taskId 任务ID
     * @return 任务结果
     */
    @Transactional(readOnly = true)
    public AgentTaskResponseDTO getTaskResult(Long taskId) {
        log.debug("获取任务结果，任务ID: {}", taskId);

        Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        AgentTask task = taskOpt.orElseThrow(); // Fixed: replaced get() with orElseThrow()
        return buildTaskResponse(task, task.getResponseData());
    }

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     */
    public void cancelTask(Long taskId) {
        log.info("取消任务，任务ID: {}", taskId);

        Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        AgentTask task = taskOpt.orElseThrow(); // Fixed: replaced get() with orElseThrow()
        if (task.getStatus() == AgentTaskStatus.RUNNING) {
            task.setStatus(AgentTaskStatus.CANCELLED);
            task.setEndTime(Instant.now());
            task.setUpdatedAt(Instant.now());
            task.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
            agentTaskRepository.save(task);
        }
    }

    /**
     * 创建Agent任务实体
     */
    private AgentTask createAgentTaskEntity(AgentTaskRequestDTO request) {
        AgentTask agentTask = new AgentTask();
        agentTask.setTenantId(request.getTenantId());
        agentTask.setTaskType(request.getTaskType());
        agentTask.setTitle(request.getTitle());
        agentTask.setDescription(request.getDescription());
        agentTask.setStatus(AgentTaskStatus.PENDING);
        agentTask.setPriority(request.getPriority() != null ? request.getPriority() : TaskPriority.NORMAL);
        agentTask.setRequestData(request.getRequestData());
        agentTask.setProgress(0);
        agentTask.setVersion(1);
        agentTask.setCreatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
        agentTask.setCreatedAt(Instant.now());
        agentTask.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
        agentTask.setUpdatedAt(Instant.now());
        agentTask.setIsDeleted(false);
        return agentTask;
    }

    /**
     * 异步执行任务
     */
    @org.springframework.scheduling.annotation.Async
    public void executeTaskAsync(AgentTask agentTask) {
        try {
            log.info("开始执行Agent任务，ID: {}, 类型: {}", agentTask.getId(), agentTask.getTaskType());

            // 更新任务状态为运行中
            updateTaskStatus(agentTask, AgentTaskStatus.RUNNING, 10);

            // 根据任务类型调用相应的业务Agent
            String result =
                switch (agentTask.getTaskType()) {
                    case REGULATION_INTERNALIZATION -> executeRegulationInternalization(agentTask);
                    case POLICY_REVIEW -> executePolicyReview(agentTask);
                    case CONTRACT_REVIEW -> executeContractReview(agentTask);
                };

            // 更新任务状态为完成
            updateTaskCompletion(agentTask, AgentTaskStatus.COMPLETED, result);

            log.info("Agent任务执行完成，ID: {}", agentTask.getId());
        } catch (Exception e) {
            log.error("Agent任务执行失败，ID: {}", agentTask.getId(), e);
            updateTaskCompletion(agentTask, AgentTaskStatus.FAILED, null);
            updateTaskError(agentTask, e.getMessage());
        }
    }

    /**
     * 执行外规内化任务
     */
    private String executeRegulationInternalization(AgentTask agentTask) {
        log.debug("执行外规内化任务，ID: {}", agentTask.getId());

        RegulationInternalizationRequestDTO request = parseRequestData(
            agentTask.getRequestData(),
            RegulationInternalizationRequestDTO.class,
            agentTask.getTenantId(),
            0L
        ); //这里是硬编码，先做测试，后期来改为正式的 employeeId（userId)

        RegulationInternalizationResponseDTO response = regulationInternalizationService.processInternalization(request);

        return convertToJson(response);
    }

    /**
     * 执行制度审查任务
     */
    private String executePolicyReview(AgentTask agentTask) {
        log.debug("执行制度审查任务，ID: {}", agentTask.getId());

        PolicyReviewRequestDTO request = parseRequestData(agentTask.getRequestData(), PolicyReviewRequestDTO.class);

        PolicyReviewResponseDTO response = policyReviewService.reviewPolicy(request);

        return convertToJson(response);
    }

    /**
     * 执行合同审查任务
     */
    private String executeContractReview(AgentTask agentTask) {
        log.debug("执行合同审查任务，ID: {}", agentTask.getId());

        ContractReviewRequestDTO request = parseRequestData(agentTask.getRequestData(), ContractReviewRequestDTO.class);

        ContractReviewResponseDTO response = contractReviewService.reviewContract(request);

        return convertToJson(response);
    }

    /**
     * 更新任务状态
     */
    @Transactional
    public void updateTaskStatus(AgentTask agentTask, AgentTaskStatus status, Integer progress) {
        // 从数据库重新获取实体以确保正确的 Hibernate Session
        AgentTask taskToUpdate = agentTaskRepository.findById(agentTask.getId())
            .orElseThrow(() -> new RuntimeException("任务不存在，ID: " + agentTask.getId()));

        // 更新状态
        taskToUpdate.setStatus(status);
        if (progress != null) {
            taskToUpdate.setProgress(progress);
        }
        if (status == AgentTaskStatus.RUNNING && taskToUpdate.getStartTime() == null) {
            taskToUpdate.setStartTime(Instant.now());
        }

        updateCommonFields(taskToUpdate);
        agentTaskRepository.save(taskToUpdate);

        log.debug("任务状态更新成功，ID: {}, 状态: {}, 进度: {}", agentTask.getId(), status, progress);
    }

    /**
     * 更新任务完成状态
     */
    @Transactional
    public void updateTaskCompletion(AgentTask agentTask, AgentTaskStatus status, String result) {
        int maxRetries = this.getMaxRetryCount();
        int retries = 0;
        while (retries <= maxRetries) {
            try {
                // 在异步环境中，必须从数据库重新获取实体以确保正确的 Hibernate Session
                AgentTask taskToUpdate = agentTaskRepository.findById(agentTask.getId())
                    .orElseThrow(() -> new RuntimeException("任务不存在，ID: " + agentTask.getId()));

                // 更新状态
                taskToUpdate.setStatus(status);
                taskToUpdate.setProgress(100);
                taskToUpdate.setEndTime(Instant.now());
                if (result != null) {
                    taskToUpdate.setResponseData(result);
                }
                if (taskToUpdate.getStartTime() != null) {
                    taskToUpdate.setExecutionTime(taskToUpdate.getEndTime().toEpochMilli() - taskToUpdate.getStartTime().toEpochMilli());
                }
                taskToUpdate.setUpdatedAt(Instant.now());
                taskToUpdate.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));

                // 直接设置状态，不进行枚举转换
                // 从数据库获取的实体应该已经有正确的枚举值

                // 尝试保存并更新原始对象的版本
                try {
                    AgentTask saved = agentTaskRepository.save(taskToUpdate);
                    agentTask.setVersion(saved.getVersion());
                } catch (Exception e) {
                    // 如果无法保存(例如事务尚未提交)，只记录日志，不抛出异常
                    log.debug("保存任务完成状态更新失败，可能任务尚未提交事务，任务ID: {}", agentTask.getId(), e);
                    // 将更新应用到原始对象，保证后续操作数据一致
                    if (taskToUpdate != agentTask) {
                        agentTask.setStatus(status);
                        agentTask.setProgress(100);
                        agentTask.setEndTime(taskToUpdate.getEndTime());
                        if (result != null) {
                            agentTask.setResponseData(result);
                        }
                        if (agentTask.getStartTime() != null) {
                            agentTask.setExecutionTime(agentTask.getEndTime().toEpochMilli() - agentTask.getStartTime().toEpochMilli());
                        }
                        agentTask.setUpdatedAt(Instant.now());
                        agentTask.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
                    }
                }
                return;
            } catch (ObjectOptimisticLockingFailureException e) {
                retries++;
                if (retries > maxRetries) {
                    log.error("更新任务完成状态失败，任务ID: {}，已超过最大重试次数: {}", agentTask.getId(), maxRetries);
                    throw e;
                }
                log.warn("乐观锁冲突，任务ID: {}，正在进行第{}次重试", agentTask.getId(), retries);
                try {
                    // 短暂延迟后重试
                    Thread.sleep(100 * retries);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试过程被中断", ie);
                }
            }
        }
    }

    /**
     * 更新任务错误信息
     */
    private void updateTaskError(AgentTask agentTask, String errorMessage) {
        int maxRetries = this.getMaxRetryCount();
        int retries = 0;
        while (retries <= maxRetries) {
            try {
                AgentTask taskToUpdate;
                try {
                    // 尝试从数据库获取最新的agentTask
                    taskToUpdate = agentTaskRepository.findById(agentTask.getId())
                        .orElse(null);
                } catch (Exception e) {
                    log.debug("从数据库获取任务信息失败，可能任务尚未提交事务，将使用传入的任务对象，任务ID: {}", agentTask.getId());
                    taskToUpdate = null;
                }

                // 如果从数据库中无法获取到任务(可能是尚未提交到数据库)，则直接使用传入的任务对象
                if (taskToUpdate == null) {
                    taskToUpdate = agentTask;
                }

                // 更新错误信息
                taskToUpdate.setErrorMessage(errorMessage);
                taskToUpdate.setUpdatedAt(Instant.now());
                taskToUpdate.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));

                // 避免直接使用可能导致类加载器问题的对象引用，而是重设优先级
                // 使用字符串值重设优先级，避免不同类加载器实例问题
                if (taskToUpdate.getPriority() != null) {
                    String priorityName = taskToUpdate.getPriority().name();
                    taskToUpdate.setPriority(TaskPriority.valueOf(priorityName));
                }

                // 尝试保存并更新原始对象的版本
                try {
                    AgentTask saved = agentTaskRepository.save(taskToUpdate);
                    agentTask.setVersion(saved.getVersion());
                } catch (Exception e) {
                    // 如果无法保存(例如事务尚未提交)，只记录日志，不抛出异常
                    log.debug("保存任务错误信息更新失败，可能任务尚未提交事务，任务ID: {}", agentTask.getId(), e);
                    // 将更新应用到原始对象，保证后续操作数据一致
                    if (taskToUpdate != agentTask) {
                        agentTask.setErrorMessage(errorMessage);
                        agentTask.setUpdatedAt(Instant.now());
                        agentTask.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
                    }
                }
                return;
            } catch (ObjectOptimisticLockingFailureException e) {
                retries++;
                if (retries > maxRetries) {
                    log.error("更新任务错误信息失败，任务ID: {}，已超过最大重试次数: {}", agentTask.getId(), maxRetries);
                    throw e;
                }
                log.warn("乐观锁冲突，任务ID: {}，正在进行第{}次重试", agentTask.getId(), retries);
                try {
                    // 短暂延迟后重试
                    Thread.sleep(100 * retries);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试过程被中断", ie);
                }
            }
        }
    }

    /**
     * 解析请求数据
     */
    private <T> T parseRequestData(String requestData, Class<T> valueType) {
        return parseRequestData(requestData, valueType, null, null);
    }

    private <T> T parseRequestData(String requestData, Class<T> valueType, Long tenantId, Long defaultEmployeeId) {
        if (requestData == null || requestData.isEmpty()) {
            return null;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.registerModule(new JavaTimeModule());
            return objectMapper.readValue(requestData, valueType);
        } catch (Exception e) {
            log.error("解析请求数据失败，租户ID: {}, 数据: {}", tenantId, requestData, e);
            throw new RuntimeException("解析请求数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 转换为JSON格式
     */
    private String convertToJson(Object data) {
        if (data == null) {
            return null;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            objectMapper.registerModule(new JavaTimeModule());
            return objectMapper.writeValueAsString(data);
        } catch (Exception e) {
            log.error("转换为JSON格式失败，数据: {}", data, e);
            throw new RuntimeException("转换为JSON格式失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建任务响应
     */
    private AgentTaskResponseDTO buildTaskResponse(AgentTask task, String responseData) {
        // 使用Builder模式创建响应对象，而不是通过mapper转换
        AgentTaskResponseDTO response = AgentTaskResponseDTO.builder()
            .taskId(task.getId())
            .taskType(task.getTaskType())
            .title(task.getTitle())
            .status(task.getStatus())
            .progress(task.getProgress())
            .result(responseData)  // 直接设置响应数据
            .startTime(task.getStartTime())
            .endTime(task.getEndTime())
            .executionTime(task.getExecutionTime())
            .errorMessage(task.getErrorMessage())
            .createdAt(task.getCreatedAt())
            .build();

        // 不尝试解析和设置特定类型的响应对象，因为AgentTaskResponseDTO可能没有这些setter方法
        // 客户端可以根据taskType和responseData自行解析特定类型的响应

        return response;
    }

    /**
     * 获取最大重试次数
     */
    private int getMaxRetryCount() {
        // 这里可以根据实际情况配置，例如从配置文件中读取
        return 3;
    }
}
